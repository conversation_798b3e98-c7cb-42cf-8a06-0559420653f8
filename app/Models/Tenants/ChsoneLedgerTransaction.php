<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Tenants;

use App\Models\TenantModel;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Grammars\MySqlGrammar;
use App\Console\Commands\Action;

/**
 * Class ChsoneLedgerTransaction
 *
 * @property int $txn_id
 * @property int $soc_id
 * @property Carbon|null $transaction_date
 * @property int|null $ledger_account_id
 * @property string|null $ledger_account_name
 * @property string|null $voucher_type
 * @property string|null $voucher_reference_number
 * @property int|null $voucher_reference_id
 * @property string|null $transaction_type
 * @property string|null $payment_mode
 * @property string|null $payment_reference
 * @property float|null $transaction_amount
 * @property string|null $other_reference_id
 * @property int|null $txn_from_id
 * @property string|null $memo_desc
 * @property int|null $is_opening_balance
 * @property int|null $is_reconciled
 * @property int|null $is_cancelled
 * @property int|null $created_by
 * @property Carbon|null $added_on
 * @property Carbon|null $value_date
 *
 * @package App\Models\Tenants
 */
class ChsoneLedgerTransaction extends TenantModel
{
    protected $table = 'chsone_ledger_transactions';
    protected $primaryKey = 'txn_id';
    public $timestamps = false;

    protected $casts = [
        'soc_id' => 'int',
        'transaction_date' => 'datetime',
        'ledger_account_id' => 'int',
        'voucher_reference_id' => 'int',
        'transaction_amount' => 'float',
        'txn_from_id' => 'int',
        'is_opening_balance' => 'int',
        'is_reconciled' => 'int',
        'is_cancelled' => 'int',
        'created_by' => 'int',
        'added_on' => 'datetime',
        'value_date' => 'datetime'
    ];

    protected $fillable = [
        'soc_id',
        'transaction_date',
        'ledger_account_id',
        'ledger_account_name',
        'voucher_type',
        'voucher_reference_number',
        'voucher_reference_id',
        'transaction_type',
        'payment_mode',
        'payment_reference',
        'transaction_amount',
        'other_reference_id',
        'txn_from_id',
        'memo_desc',
        'is_opening_balance',
        'is_reconciled',
        'is_cancelled',
        'created_by',
        'added_on',
        'value_date'
    ];

    public function checkIfCompanyExists($id)
    {
        $result = DB::connection('tenant')->table('chsone_ledger_transactions')
            ->where('soc_id', $id)->exists();

        if ($result && $result != 0) {
            // The ID exists in the table
            return "Vendor $id exists in the table.";
        } else {
            // The ID does not exist in the table
            return false;
        }
    }
    public function getLedgertransaction($id)
    {
        $result = DB::connection('tenant')->table($this->table)->select()
            ->where('soc_id', $id)->get();

        return $result;
    }

    public function getLedgerTransactions($soc_id, $ledger_id, $criteria, $entity = 'ledger', $offset = 0, $length = PAGE_NUMBER)
    {
        $result_array = [];
        $filter_array = [];
        $sort_array = [];
        $criteria_clause = '';

        foreach ($criteria as $control => $condition) {
            if (!empty($condition)) {
                $criteria_clause .= empty($criteria_clause) ? $condition : ' AND ' . $condition;
            }
        }

        if (!empty($criteria_clause)) {
            $filter_array[] = $criteria_clause;
        }

        switch ($entity) {
            case 'ledger':
                array_push($sort_array, 'a.transaction_date DESC');
                array_push($sort_array, 'a.added_on DESC');
                array_push($filter_array, 'a.ledger_account_id=' . $ledger_id);
                array_push($filter_array, 'a.soc_id=' . $soc_id);
                array_push($filter_array, 'transaction_amount != 0.00');
                break;
            case 'main':
            case 'group':
                array_push($sort_array, 'a.ledger_account_id ASC');
                array_push($sort_array, 'a.transaction_date DESC');
                array_push($sort_array, 'a.added_on DESC');
                array_push($filter_array, 'a.ledger_account_id IN(' . $ledger_id . ')');
                array_push($filter_array, 'a.soc_id=' . $soc_id);
                array_push($filter_array, 'transaction_amount != 0.00');
                break;
        }

        $sql = "SELECT SQL_CALC_FOUND_ROWS a.*, b.* FROM chsone_ledger_transactions as a 
                LEFT JOIN chsone_grp_ledger_tree as b ON a.ledger_account_id = b.ledger_account_id 
                WHERE " . implode(' AND ', $filter_array) . " 
                ORDER BY " . implode(', ', $sort_array) . " 
                LIMIT $offset, $length";

        $transaction_array = DB::connection('tenant')->select($sql);

        $found_rows_array = DB::connection('tenant')->select('SELECT FOUND_ROWS() as total_records');

        $least_ledger_ac_id = [];
        foreach ($transaction_array as $transaction) {
            if (!isset($least_ledger_ac_id[$transaction->ledger_account_id]) || ($least_ledger_ac_id[$transaction->ledger_account_id] > $transaction->txn_id)) {
                $least_ledger_ac_id[$transaction->ledger_account_id] = $transaction->txn_id;
            }
        }

        array_push($sort_array, 'a.transaction_date DESC');
        array_push($sort_array, 'a.added_on DESC');
        array_push($sort_array, 'a.ledger_account_id');
        array_push($sort_array, 'a.txn_id DESC');

        $column_array = ['a.txn_id', 'a.ledger_account_id', 'SUM(IF(a.transaction_type = "cr", a.transaction_amount, 0)) as credit_amount', 'SUM(IF(a.transaction_type = "dr", a.transaction_amount, 0)) as debit_amount'];

        $filter_array = [];
        if (!empty($criteria_clause)) {
            $filter_array[] = $criteria_clause;
        }
        array_push($filter_array, 'a.ledger_account_id IN(' . $ledger_id . ')');
        array_push($filter_array, 'a.soc_id=' . $soc_id);
        array_push($filter_array, 'transaction_amount !=0.00');
        array_push($filter_array, 'a.transaction_date <="' . date('Y-m-d') . '"');

        $sql = "SELECT " . implode(', ', $column_array) . " FROM chsone_ledger_transactions as a 
                LEFT JOIN chsone_grp_ledger_tree as b ON a.ledger_account_id = b.ledger_account_id 
                WHERE " . implode(' AND ', $filter_array) . " 
                GROUP BY a.ledger_account_id";

        $transaction_total = DB::connection('tenant')->select($sql);

        $prev_transaction_total = [];
        if (!empty($transaction_array) && ($offset + $length < $found_rows_array[0]->total_records)) {
            foreach ($transaction_total as $total) {
                $x_where_clause = '';
                if (!empty($criteria_clause)) {
                    $x_where_clause .= empty($x_where_clause) ? str_replace('a.', 'x.', $criteria_clause) : ' AND ' . str_replace('x.', 'a.', $criteria_clause);
                }

                $filter_array = [];
                if (!empty($criteria_clause)) {
                    $filter_array[] = $criteria_clause;
                }
                array_push($filter_array, 'a.ledger_account_id IN(' . $total->ledger_account_id . ')');
                array_push($filter_array, 'a.soc_id=' . $soc_id);
                array_push($filter_array, 'a.txn_id NOT IN (SELECT tmp.txn_id FROM (SELECT x.ledger_account_id, x.txn_id FROM chsone_ledger_transactions as x WHERE ' . (!empty($x_where_clause) ? $x_where_clause . ' AND ' : '') . ' x.ledger_account_id IN(' . $total->ledger_account_id . ') AND x.soc_id="' . $soc_id . '" AND x.transaction_amount != 0 ORDER BY x.transaction_date DESC, x.added_on DESC LIMIT 0,' . ($offset + $length) . ') as tmp)');

                array_push($filter_array, 'transaction_amount !=0.00');

                $sql = "SELECT " . implode(', ', $column_array) . " FROM chsone_ledger_transactions as a 
                        LEFT JOIN chsone_grp_ledger_tree as b ON a.ledger_account_id = b.ledger_account_id 
                        WHERE " . implode(' AND ', $filter_array) . " 
                        GROUP BY a.ledger_account_id";

                $previous_ledger_transaction_total = DB::connection('tenant')->select($sql);
                if (!empty($previous_ledger_transaction_total)) {
                    $prev_transaction_total = array_merge($prev_transaction_total, $previous_ledger_transaction_total);
                }
            }
        }

        $result_array = [
            'transaction_array' => $transaction_array,
            'total_records' => $found_rows_array[0]->total_records,
            'transaction_total' => $transaction_total,
            'prev_transaction_total' => $prev_transaction_total,
            'total_records' => $found_rows_array[0]->total_records
        ];

        return $result_array;
    }

    public function getLedgerTransactionsByFY($data)
    {
        $soc_id = $data['soc_id'];
        $ledger_id = $data['ledger_id'];
        $criteria = $data['criteria'];
        $entity = !empty($data['entity']) ? $data['entity'] : 'ledger';
        $offset = !empty($data['offset']) ? $data['offset'] : 0;
        $length = !empty($data['length']) ? $data['length'] : PAGE_NUMBER;
        $start_date = $data['start_date'];
        $end_date = $data['end_date'];

        $result_array = [];
        $filter_array = [];
        $sort_array = [];
        $column_array = [];
        $criteria_clause = '';

        foreach ($criteria as $control => $condition) {
            if (!empty($condition)) {
                $criteria_clause .= empty($criteria_clause) ? $condition : ' AND ' . $condition;
            }
        }

        if (!empty($criteria_clause)) {
            $filter_array[] = $criteria_clause;
        }

        switch ($entity) {
            case 'ledger':
                $sort_array[] = 'a.transaction_date DESC';
                $sort_array[] = 'a.added_on DESC';
                $filter_array[] = 'a.ledger_account_id=' . $ledger_id;
                $filter_array[] = 'a.soc_id=' . $soc_id;
                $filter_array[] = 'a.transaction_amount !=0.00';
                $filter_array[] = 'a.transaction_date BETWEEN "' . $start_date . '" AND "' . $end_date . '"';
                break;
            case 'main':
            case 'group':
                $sort_array[] = 'a.ledger_account_id ASC';
                $sort_array[] = 'a.transaction_date DESC';
                $sort_array[] = 'a.added_on DESC';
                $filter_array[] = 'a.ledger_account_id IN(' . $ledger_id . ')';
                $filter_array[] = 'a.soc_id=' . $soc_id;
                $filter_array[] = 'a.transaction_amount !=0.00';
                $filter_array[] = 'a.transaction_date BETWEEN "' . $start_date . '" AND "' . $end_date . '"';
                break;
        }

        $filter_string = implode(' AND ', $filter_array);
        $query = DB::connection('tenant')->table('chsone_ledger_transactions as a')
            ->leftJoin('chsone_grp_ledger_tree as b', 'a.ledger_account_id', '=', 'b.ledger_account_id')
            ->select('a.*', 'b.*')
            ->whereRaw($filter_string);

        $total_records = $query->count();

        $transaction_array = $query->skip($offset)->take($length)->get();

        $least_ledger_ac_id = [];

        foreach ($transaction_array as $transaction) {
            if (!isset($least_ledger_ac_id[$transaction->ledger_account_id]) || ($least_ledger_ac_id[$transaction->ledger_account_id] > $transaction->txn_id)) {
                $least_ledger_ac_id[$transaction->ledger_account_id] = $transaction->txn_id;
            }
        }
        $sort_array[] = 'a.transaction_date DESC';
        $sort_array[] = 'a.added_on DESC';
        $sort_array[] = 'a.ledger_account_id';
        $column_array[] = 'a.txn_id, a.ledger_account_id, SUM(IF(a.transaction_type = "cr", a.transaction_amount, 0)) as "credit_amount", SUM(IF(a.transaction_type = "dr", a.transaction_amount, 0)) as "debit_amount"';

        // $column_array = [
        //     'a.txn_id',
        //     'a.ledger_account_id',
        //     DB::raw('SUM(IF(a.transaction_type = "cr", a.transaction_amount, 0)) as credit_amount')->getValue(new MySqlGrammar),
        //     DB::raw('SUM(IF(a.transaction_type = "dr", a.transaction_amount, 0)) as debit_amount')->getValue(new MySqlGrammar),
        // ];

        if (!empty($criteria_clause)) {
            $filter_array[] = $criteria_clause;
        }

        $filter_array[] = 'a.ledger_account_id IN(' . $ledger_id . ')';
        $filter_array[] = 'a.soc_id=' . $soc_id;
        $filter_array[] = 'transaction_amount != 0.00';
        $filter_array[] = 'a.transaction_date BETWEEN "' . $start_date . '" AND "' . $end_date . '"';

        $query = DB::connection('tenant')->table('chsone_ledger_transactions as a')
        ->leftJoin('chsone_grp_ledger_tree as b', 'a.ledger_account_id', '=', 'b.ledger_account_id')
        ->select('a.txn_id', 'a.ledger_account_id', DB::raw('SUM(IF(a.transaction_type = "cr", a.transaction_amount, 0)) as credit_amount'), DB::raw('SUM(IF(a.transaction_type = "dr", a.transaction_amount, 0)) as debit_amount'))
        ->whereRaw(implode(' AND ', $filter_array))
        ->groupBy('a.ledger_account_id');
                
        $transaction_total = $query->get();

        $filter_array = [];
        
        $prev_transaction_total = [];

        if (!empty($transaction_array) && ($offset + $length < $total_records)) {
            foreach ($transaction_total as $total) {
                $x_where_clause = '';

                if (!empty($criteria_clause)) {
                    $x_where_clause .= empty($x_where_clause) ? str_replace('a.', 'x.', $criteria_clause) : ' AND ' . str_replace('x.', 'a.', $criteria_clause);
                }

                array_push($sort_array, 'a.transaction_date DESC');
                array_push($sort_array, 'a.added_on DESC');
                array_push($sort_array, 'a.ledger_account_id');
                array_push($column_array, 'a.ledger_account_id, SUM(IF(a.transaction_type = "cr", a.transaction_amount, 0)) as "credit_amount", SUM(IF(a.transaction_type = "dr", a.transaction_amount, 0)) as "debit_amount"');
                if (!empty($criteria_clause)) {
                    $filter_array[] = $criteria_clause;
                }

                $filter_array[] = 'a.ledger_account_id IN(' . $total->ledger_account_id . ')';
                $filter_array[] = 'a.soc_id=' . $soc_id;
                $filter_array[] = 'a.txn_id NOT IN (SELECT tmp.txn_id FROM (SELECT x.ledger_account_id, x.txn_id FROM chsone_ledger_transactions as x WHERE ' . (!empty($x_where_clause) ? $x_where_clause . ' AND ' : '') . ' x.ledger_account_id IN(' . $total->ledger_account_id . ') AND x.soc_id="' . $soc_id . '" AND x.transaction_amount != 0 ORDER BY x.transaction_date DESC, x.added_on DESC LIMIT 0,' . ($offset + $length) . ') as tmp)';

                $filter_array[] = 'transaction_amount !=0.00';
                array_push($filter_array, 'a.transaction_date BETWEEN "' . $start_date . '" AND "' . $end_date . '"');


                $query = DB::connection('tenant')->table('chsone_ledger_transactions as a')
                ->leftJoin('chsone_grp_ledger_tree as b', 'a.ledger_account_id', '=', 'b.ledger_account_id')
                ->select('a.txn_id', 'a.ledger_account_id', DB::raw('SUM(IF(a.transaction_type = "cr", a.transaction_amount, 0)) as credit_amount'), DB::raw('SUM(IF(a.transaction_type = "dr", a.transaction_amount, 0)) as debit_amount'))
                ->whereRaw(implode(' AND ', $filter_array))
                ->groupBy('a.ledger_account_id');

                $previous_ledger_transaction_total = $query->get()->toArray();

                if (!empty($previous_ledger_transaction_total)) {
                    $prev_transaction_total = array_merge($prev_transaction_total, $previous_ledger_transaction_total);
                }
            }
        }

        $result_array = [
            'transaction_array' => $transaction_array,
            'total_records' => $total_records,
            'transaction_total' => $transaction_total,
            'prev_transaction_total' => $prev_transaction_total,
            'total_records' => $total_records
        ];
        return $result_array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0, $soc_id = '')
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1, "", $soc_id);
    }
    
    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            $mode = ($from_txn_id) ? MODE_TO : MODE_FROM;
            $type_txn = ($from_txn_id) ? "cr" : "dr";
        }

        if ($is_opning == 1) {
            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->where('transaction_date', $txn_date)
                ->first();
            $txn->value_date = $txn_date;
            $txn->txn_id = ($txn_entrt && $txn_entrt->txn_id) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::where('txn_id', $txn->txn_id)->first();
            }
        }
        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = $txn_amt ?? $txn->transaction_amount ?? 0;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        $txn->created_by = 2894;
        if (!empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = now();
        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return $txn->getMessages();
        }
    }
}
