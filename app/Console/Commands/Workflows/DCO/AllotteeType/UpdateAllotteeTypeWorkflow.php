<?php

namespace App\Console\Commands\Workflows\DCO\AllotteeType;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class UpdateAllotteeTypeWorkflow extends Workflow
{
    protected $signature = 'workflow:updateAllotteeType {input}';

    protected $description = 'Update Allottee Type';

    protected $rules = [
        'member_type_name' => 'required'
    ];

    protected $rulesMessage = [
        'member_type_name.required' => 'Please provide a valid allottee type'
    ];

    public function apply()
    {
        $id = $this->input['id'];
        if (!$id || $id == '' || $id == ':id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid id';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $allotteeTypeDetails = $this->action('datasource:updateAllotteeType', $this->pointer, $this->request);
        }
    }
}