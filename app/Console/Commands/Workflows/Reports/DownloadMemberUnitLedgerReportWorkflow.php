<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadMemberUnitLedgerReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadMemberUnitLedgerReport {input}';

    protected $description = 'Download Member Unit Ledger Report Workflow';

    /*protected $rules = [
        'from_date' => 'nullable|date_format:Y-m-d',
        'to_date' => 'nullable|date_format:Y-m-d',
        'unit' => 'required',
    ];

    protected $rulesMessage = [
        'from_date.date_format' => 'From date should be in Y-m-t format',
        'to_date.date_format' => 'To date should be in Y-m-t format',
        'unit.required' => 'Unit is required',
    ];*/

    protected $formatter = [
        'id' => '',
        'transaction_date' => '',
        'particulars' => '',
        'voucher_type' => '',
        'voucher_reference_number' => '',
        'debit' => '',
        'credit' => '',
    ];

    /*protected $formatterByKeys = [
        'id'
    ];*/

    protected $headings = [
        'id',
        'transaction_date',
        'particulars',
        'voucher_type',
        'voucher_reference_number',
        'debit',
        'credit',
    ];

    public function apply()
    {

        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $member_unit_ledger = $this->action('datasource:MembersUnitLedgerReport', $this->pointer, $this->request);
           
            $this->data = [];
            if($type == 'excel')
            {
                // Map data to match headings and add serial id
                $exportRows = [];
                $id = 1;
                foreach ($member_unit_ledger[0] as $row) {
                    $exportRows[] = [
                        'id' => $id++,
                        'transaction_date' => $row['transaction_date'] ?? '',
                        'particulars' => $row['particulars'] ?? '',
                        'voucher_type' => $row['voucher_type'] ?? '',
                        'voucher_reference_number' => $row['voucher_reference_number'] ?? '',
                        'debit' => isset($row['debit']) ? ($row['debit'] == 0 ? '0' : $row['debit']) : '',
                        'credit' => isset($row['credit']) ? ($row['credit'] == 0 ? '0' : $row['credit']) : '',
                    ];
                }
                // Append total row if present
                if (!empty($member_unit_ledger[1][0])) {
                    $totals = $member_unit_ledger[1][0];
                    $exportRows[] = [
                        'id' => 'Total',
                        'transaction_date' => '',
                        'particulars' => '',
                        'voucher_type' => '',
                        'voucher_reference_number' => '',
                        'debit' => isset($totals['total_debit']) ? ($totals['total_debit'] == 0 ? '0' : $totals['total_debit']) : '',
                        'credit' => isset($totals['total_credit']) ? ($totals['total_credit'] == 0 ? '0' : $totals['total_credit']) : '',
                    ];
                }
                $data = $this->hitCURLForGenerateCSV($exportRows, $this->headings, 'member_unit_ledger_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($member_unit_ledger, $this->headings, 'member_unit_ledger');
                
                $this->data['url'] = $data['data'];
            }
        }
    }
}