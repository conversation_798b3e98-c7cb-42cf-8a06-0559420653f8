<?php

define("ROLE_ADMIN", "admin");
define("ROLE_MEMBER", "member");
define("ROLE_STAFF", "staff");
define("ROLE_VENDOR", "vendor");
define("TYPE_USER_ADMIN", "admin");
define("PHALCON_CHECK_BOX_ON", "on");
define("ACL_ACTION_TYPE_READ", "read");
define("ACL_ACTION_TYPE_WRITE", "write");
define("ACL_ACTION_TYPE_DELETE", "delete");
define("ROLES_EDIT_FORM", "roles/edit/");
define("ROLES_ADD_FORM", "roles/add");
define("DIAL2VERIFY_API_KEY", 'RA$A59D5EF6-E586-11E3-9344-002590C2D93A');
define("SOC_UNITS_TIMES_ONE", 1);
define("SOC_UNITS_TIMES_FIVE", 5);
define("NON_MOBILE_DEVICE", "desktop");
define("MOBILE_DEVICE", "mobile");
define("TAB_DEVICE", "tab");
define("MEMBER_BULK_ADD_COUNT", 15);
define("EXPENSE_TRACKER_ROWS", 1);
define("BROWSCAP_CACHE_DIR", "var/cache/");
define("VIZLOG_APP_ID", 5);

define("DIAL2VERIFY_URL", "http://engine.dial2verify.in/Integ/UserLayer/DataFeed_APIV2.dvf?SID=");
define("CHSONE_IMAGE_URL", "https://www.chsone.in/app/public/assets/img/");
define("DIAL2VERIFY_API_URL", "http://engine.dial2verify.in/Integ/API.dvf?notify=http://engine.dial2verify.in/Integ/CatchAll.dvf&e-notify=<EMAIL>&out=JSON&cn=IN&");

define("ENTITY_TYPE_LEDGER", "ledger");
define("ENTITY_TYPE_GROUP", "group");
define("ENTITY_TYPE_MAIN", "main");
define("ACC_TYPE_BANK", "bank");
define("ACC_TYPE_CASH", "cash");
define("SUNDRY_DR", "Sundry Debtors");
define("SUNDRY_CR", "Sundry Creditors");
define("SUNDRY_DEBTORS_GROUP", "sundrydebtors");
define("HEAD_GROUP", "head_group");
define("USER", "user");
define("SYSTEM", "system");
define("VOUCHER_ALL", "all");
define("VOUCHER_JOURNAL", "journal");
define("VOUCHER_CONTRA", "contra");
define("VOUCHER_PAYMENT", "payment");
define("VOUCHER_PURCHASE", "purchase");
define("VOUCHER_RECEIPT", "receipt");
define("VOUCHER_EXPENSE", "expense");
define("VOUCHER_CREDIT", "credit note");
define("VOUCHER_SALES", "income");
define("VOUCHER_DEBIT", "debit note");
define("ACCOUNTSETTING", "accountSetting");
define("MODE_TO", "to");
define("MODE_FROM", "from");
define("ACTIVE", 1);

define("UNIT_TYPE_NOT_AVAILABLE", "Unit Type not available.");
define("CHANG_PASSWORD_TXT", "changePassword");
define("MEMBER_TENANT", 4);
define("MEMBER_PRIMARY", 1);

define("ADMIN_MODULE", "admin");
define("ACCOUNT_MODULE", "accn");
define("MAIN_MODULE", "chsone");
define("STAFF_MODULE", "staff");
define("EXPENSES_MODULE", "expense");

define("SUPER_ADMIN", 1);
define("USER_AGE_LIMIT", 18);
define('GROUP_CHILDREN_OPTION_DIS', 'displaychildrenoptionsinselbox');
define('MAKE_HEAD_GROUP', 'Make Head Group');
define("ACCOUNTVOUCHER", 'account_voucher');
define("HEAD_GROUP_VAL", 0);
define("PRIMARY_MEMBER_ERR_MSG", "Only one primary member is allowed for a flat/shop.");
define("USERNAME_EXISTS", 'Username already exists');
define("EMAIL_EXISTS", "Email address already exists");
define("MOBILNO_EXISTS", "Mobile Number is already registered with some other user");
define("MEMBER_EMAIL_EXISTS", "Member email address already exists");
define("MEMBER_MOBILNO_EXISTS", "Member mobile number already exists");
define("SELECT_MIN_ONE_MEM", "Please select minimum one record");
define("MEMBER_ADD_SUCC", "Member(s) added successfully.");
define("MEMBER_EDIT_SUCC", "Member(s) updated successfully.");
define("TAX_ADD_SUCC", "Tax added successfully.");
define("TAX_EDIT_SUCC", "Tax updated successfully.");
define("RULE_ADD_SUCC", "Rule added successfully.");
define("RULE_EDIT_SUCC", "Rule updated successfully.");
define("GENERALSETTING_ADD_SUCC", "General setting added successfully.");
define("GENERALSETTING_EDIT_SUCC", "General setting  updated successfully.");
define("INCOMEACCOUNT_ADD_SUCC", "Income Account setting added/updated successfully.");
define("PARTICULAR_SETTINGS_ADD_SUCC", "Particular setting added/updated successfully.");
define("COMMONBILLING_ADD_SUCC", "Common Billing setting added successfully.");
define("COMMONBILLING_EDIT_SUCC", "Common Billing setting updated successfully.");
define("COMMONBILL_ADD_SUCC", "Common Billing added successfully.");
define("COMMONBILL_ADD_ERR", "Unable to add common bill.");
define("COMMONBILL_EDIT_SUCC", "Common Billing added successfully.");
define("COMMONBILL_EDIT_ERR", "Unable to update common bill.");
define("FACILITY_INCOMEACCOUNT_ADD_SUCC", "Income Account for facility setting added/updated successfully.");
define("PAGE_NUMBER", 20);
define("REPORT_PAGE_NUMBER", 20);
define("SWIMMING_POOL", "swimming_pool");
define("GARDEN", "garden");
define("BADGEINFO", "Testing");
define("REQ_FIELD", "<span class=\"red\">*</span>");
define("MOBILE_NO_LENGTH", 15);
define("MOBILE_NO_LENGTH_MIN", 5);
define("PHONE_NO_LENGTH", 11);

define("GR_ADD_SUCC", "Group Added Successfully.");
define("GR_ED_SUCC", "Group Edited Successfully.");
define("BILLABLE_ADD_SUCC", "Billable Items Added Successfully.");
define("BILLABLE_EDIT_SUCC", "Billable Items Updated Successfully.");
define("BILLABLE_DELETE_SUCC", "Billable Items Deleted Successfully.");
define("BANK_ADD_SUCC", "Bank Account Added Successfully.");
define("BANK_EDIT_SUCC", "Bank Account updated Successfully. (Account details will not be updated if benecode is generated)");
define("CASH_ADD_SUCC", "Cash Account Added Successfully.");
define("CASH_EDIT_SUCC", "Cash Account Updated Successfully.");
define("LEDG_ADD_SUCC", "Ledger Added Successfully.");
define("LEDG_ED_SUCC", "Ledger Edited Successfully.");

define("CONTRA_ADD_SUCC", "Contra Entry Added Successfully.");
define("LEG_EDIT_SUCC", "Ledger Transaction updated Successfully");
define("CREDIT_ADD_SUCC", "Credit Note Entry Added Successfully.");
define("PAYMT_ADD_SUCC", "Payment Entry Added Successfully.");
define("RECEIPT_ADD_SUCC", "Receipt Entry Added Successfully.");
define("DEBIT_ADD_SUCC", "Debit Note Entry Added Successfully.");
define("SALES_ADD_SUCC", "Sales Entry Added Successfully.");
define("EXPENSE_ADD_SUCC", "Expense Entry Added Successfully.");
define("JOURNAL_ADD_SUCC", "Journal Entry Added Successfully.");
define("PURCHASE_ADD_SUCC", "Purchase Entry Added Successfully.");
define("VENDOR_ADD_SUCC", "Vendor Added Successfully.");
define("VENDOR_EDIT_SUCC", "Vendor Updated Successfully.");
define("DB_MASTER", "dbMaster");
define("DB_SLAVE", "dbSlave");

define("FORM_ERRORS", "Please correct validation errors in the form.");
define("ASSET_COST_CURRENCY", "<i class='fa fa-inr'></i>");
define("FORGOTPSD_ERR_MSG", "The information you entered is incorrect.");
define("FORGOTPSD_SAVE_ERR_MSG", "Oops!! This process is currently suspended. Kindly contact CHSONE support.");
define("FORGOTPSD_SUCC_MSG", "Message was sent successfully..Please check your email account");
define("RESET_PASSWORD_INVALID_LINK", "The link that you provided was found invalid. Please try again for a new link to reset your password.");

define("STAFF_GROUP", "staff");
define("CURRENT_ASSETS_GROUP", "Current Assets");

define("VENDORBILL_SUCC_MSG", "Vendor bill is added successfully.");
define("CREDIT", "credit");
define("VENDORBILL_PAYAMOUNT_ERR", "Payment amount should be less than or equal to payment due amount");
define("VENDORBILLPAYMENT_SUCC_MSG", "Payment is completed successfully.");
define("INCOME", "income");
define("LIABILITY", "liability");
define("ASSET", "asset");
define("EXPENSE", "expense");
define("AssetExist", "Records exists under this category");
define("VENDORBILL_PAYMENT", "Vendor Bill Payment");
define("INACTIVE", 0);
define("USER_MODULE", "User");

define("APP_PATH", realpath(dirname(dirname(dirname(__FILE__)))) . '/'); // "/var/www/html/chsone_admin/"

define("PO_FILE_UPLOAD_PATH", APP_PATH . "var/po_uploads/");
define("VARLOGS", APP_PATH . "var/logs/");
define("CURRENCY_INR", "INR");
define("OTHEREXP_ADDED_SUCC_MSG", "Miscellaneous expense is added succesfully.");
define("OTHEREXP_UPDATED_SUCC_MSG", "Other expense is updated succesfully.");
define("FILE_FORMAT_PDF", "pdf");
define("FILE_FORMAT_JPG", "jpg");
define("VENDORSTATUSCHANGED_SUCC_MSG", "Vendor status is changed successfully.");
define("TAXSTATUSCHANGED_SUCC_MSG", "TAX status is changed successfully.");
define("VOUCHER_CASH", "bill_cash");
define("VENDOR_GROUP", "vendor");
define("VENDOR_ADVANCE_GROUP", "Advances");
define("CARD_GROUP", "Card");
define("VOUCHER_CARD", "bill_card");
//define("EXPENSES_ROOT_LEDGER_ID", 34);
define("MISCELLANEOUS_LEDGER", "Miscellaneous");
define("TAX_LEDGER", "Taxes");
define("EMAIL_SMTP_AUTH_TRUE", true);
define("EMAIL_SMTP_USERNAME", "<EMAIL>"); //"<EMAIL>"
//define("EMAIL_SMTP_PASSWORD", "4v2Qz2Gl1LQquwnXsm502g"); //"cPDMFxp3"
define("EMAIL_SMTP_PASSWORD", "P-_Owz4vlPNJJk5kigTlAA");
define("EMAIL_SMTP_SECURE", "tls");
define("EMAIL_PORT", 587);
define("EMAIL_SMTP_SECURE_ONEAPP", "tls");
define("EMAIL_PORT_ONEAPP", 587);
define("EMAIL_SMTP_USERNAME_ONEAPP", "AKIARRSRIRX2WZGQYB7J"); //"<EMAIL>"
define("EMAIL_SMTP_PASSWORD_ONEAPP", "BOLPtocopG5HQy1okGnhbeOsgk05mUeBnFb4VVFSmbAK"); //"cPDMFxp3"
define("EMAIL_FROM", "<EMAIL>");
define("EMAIL_FROM_NAME", "CHSONE");
define("EMAIL_FROM_ONEAPP", "<EMAIL>");
define("EMAIL_FROM_NAME_ONEAPP", "ONEAPP");
define("EMAIL_HTML_TRUE", false);
define("EMAIL_SMTP_DEBUG", 0);
#define("EMAIL_SMTP_SERVER", "smtp.chsone.co.in");
define("EMAIL_SMTP_SERVER", "smtp.mandrillapp.com");
define("EMAIL_SMTP_SERVER_ONEAPP", "email-smtp.ap-south-1.amazonaws.com");
define("EMAIL_TEMPLATE_FROM_BACKEND_VIEW", APP_PATH . "app/societySetup/views/"); // "app/societySetup/views/"
define("EMAIL_TEMPLATE_FOLDER", "emailTemplates");
define("EMAIL_TEMPLATE_SOCIETY_REGISTRATION_SUCCESS", "socregistration");
define("EMAIL_TEMPLATE_USER_REGISTRATION_SUCCESS", "socregistration");
define("EMAIL_TEMPLATE_USERCHANGE_PASSWORD", "socregistration");
define("EMAIL_TEMPLATE_MEMBER_REGISTRATION_SUCCESS", "socregistration");

define("MEMBER_REGISTER_SUCC_MSG", "Member is created successfully.Please check your email account..");
define("MEMBER_REGISTER_EMAIL_MSG", "Member is created successfully.We will contact you soon..");
define("EMAIL_TEMPLATE_RESET_PASSWORD", "socregistration");
define("PURCHASE_ORDER_NEEDS_YOUR_APPROVAL", "New Purchase Order needs your Approval");
define("APPROVAL_STATUS", "approved");
define("PURCHASE_ORDER_PLACED", "Purchase Order Placed");
define("EXPORT_EXCEL", "excel");
define("EXPORT_PDF", "pdf");
define("EXPORT_PRINT", "print");

define("BUILDING_ADD_SUCC_MSG", "Building is added successfully.");
define("BUILDING_EDIT_SUCC_MSG", "Building is updated successfully.");
define("ENTRY_SUCCESS", "Entry Successfully Added.");
define("UPLOAD_FILES_BASE_PATH", APP_PATH . "var/");
define("UPLOAD_FILES_BASE_PATH_WEBSITE", APP_PATH);
define("UPLOAD_IMAGE_BASE_PATH_WEBSITE", APP_PATH . "public/socweb/");
define("MODULE_ADMIN_DASHBOARD", "Admin_Dashboard");
define("MODULE_FRONTEND_DASHBOARD", "User_Dashboard");
define("VENDORBILL_FILE_UPLOAD_PATH", APP_PATH . "var/vendorbills/");
define("STAFF_IMAGE_FILE_UPLOAD_PATH", APP_PATH . "var/staff_image/");
define("STAFF_PROOF_FILE_UPLOAD_PATH", APP_PATH . "var/staff_proof/");

define("MEMBER_ASSOCIATE", 2);
define("ASSOCIATE_MEMBER_ERR_MSG", "<label>Please add primary member to the unit first.</label>");

define("USER_SOURCE", "Website");
define("ACCOUNT_RECEIVABLE_GROUP", "accountreceivable");
define("MEMBER_CONTRI", "membercontri");
define("DEFAULT_VALUE_NA", "NA");
define("NONAME", "noname");
define("COMMON_AMENITY", "commonamenity");
define("VOUCHER_BANK", "bill_bank");
define("DEFAULT_PAGE_NO", 1);

define("EMAIL_SUBJECT_MEMBER_APPROVED", "Congratulations! Member account is approved..");
define("EMAIL_TEMPLATE_MEMBER_APPROVED", "socregistration");
define("MEMBER_APPROVED_EMAIL_MSG", "You account is approved by admin...");

define("EMAIL_SUBJECT_MEMBER_DELETE", "Sorry..Your account has been deleted");
define("EMAIL_TEMPLATE_MEMBER_DELETE", "socregistration");
define("MEMBER_DELETE_EMAIL_MSG", "We are sorry.. You account has been deleted by admin...");

define("EMAIL_SUBJECT_MEMBER_REGISTRATION", "Member account is created successfully.");
define("MEMBER_REGISTERED_EMAIL_MSG", "Member account is created successfully.");
define("EMAIL_SUBJECT_USER_ACCOUNT", "User account is created successfully.");

define("USER_REGISTERED_EMAIL_MSG", "User account is created and login details are - <br>");

define("EMAIL_SUBJECT_SOCIETY_REGISTRATION", "Society is created successfully.");
define("SOCIETY_REGISTERED_EMAIL_MSG", "Society is registered successfully..and user account is created with login details as - <br>");

define("EMAIL_SUBJECT_CHANGE_PASSWORD", "Your password is changed successfully");
define("CHANGE_PASSWORD_EMAIL_MSG", "Your password is changed successfully <br> Your login details are - <br>");

define("EMAIL_SUBJECT_FORGOT_PASSWORD", "Reset Your Password.");
define("FORGOT_PASSWORD_EMAIL_MSG", "Please click the given url to reset your password :");


define("SUCC_MSG_STATUS_CHANGED", "Status is changed.");
define("SUCC_MSG_ASSETCATE_ADDED", "Asset category is added successfully.");
define("SUCC_MSG_ASSETCATE_UPDATED", "Asset category is updated successfully.");
define("SUCC_MSG_ASSETCATE_DELETED", "Asset category is deleted successfully.");
define("SUCC_MSG_ASSET_ADDED", "Asset is added successfully.");
define("SUCC_MSG_ASSET_UPDATED", "Asset is updated successfully.");
define("SUCC_MSG_CATEGORY_ADDED", "category is added successfully.");
define("SUCC_MSG_CATEGORY_UPDATED", "category is updated successfully.");
define("SUCC_MSG_CATEGORY_DELETED", "Inventory category is deleted.");
define("SUCC_MSG_MEMBERTYPE_ADDED", "Member type is added successfully.");
define("SUCC_MSG_MEMBERTYPE_UPDATED", "Member type is updated successfully.");
define("SUCC_MSG_MEMBERTYPE_DELETED", "Member type is deleted.");
define("SUCC_MSG_INVENTORY_ADDED", "Inventory is added successfully.");
define("SUCC_MSG_INVENTORY_UPDATED", "Inventory is updated successfully.");

define("SUCC_MSG_UNITS_TIMES_UPDATED", "Units is updated successfully.");
define("SUCC_MSG_UNITS_TIMES_ADDED", "Units is added successfully.");
define("FAIL_MSG_UNITS_MODIFIED_TIME", "You can not data. Please add this data again.");

define("REJECT", 2);
define("DASHBOARD_MOUSEOVER_TEXT_USERMANAGEMENT", "Manage Users");
define("DASHBOARD_MOUSEOVER_TEXT_STAFFMANAGER", "Manage Staffs");
define("DASHBOARD_MOUSEOVER_TEXT_VENDORMANAGER", "Vendors and Aging Report ");
define("DASHBOARD_MOUSEOVER_TEXT_SOCIETY", "Manage Buildings, Units and Members");
define("DASHBOARD_MOUSEOVER_TEXT_ACCOUNTS", "Your Books of Account, Ledgers , Bank / Cash Account and Balance Sheet");
define("DASHBOARD_MOUSEOVER_TEXT_EXPENSETRACKER", "Track General expanse and Vendor Bills");
define("DASHBOARD_MOUSEOVER_TEXT_ASSETNINVENTORY", "Assets & Inventory");
define("DASHBOARD_MOUSEOVER_TEXT_INCOMETRACKER", "Track Member and Non Member Income");
define("DASHBOARD_MOUSEOVER_TEXT_TRANSACTION", "All Vouchers and Track Asset & Inventory");
define("DASHBOARD_MOUSEOVER_TEXT_HELPDESK", "Resolve Member Queries and Issues");
define("DASHBOARD_MOUSEOVER_TEXT_PARKING", "Vehicle Registrations and In-Out Tracker");
define("DASHBOARD_MOUSEOVER_TEXT_FACILITYSETTINGS", "Manage common facility booking");
define("MEMBER_APPROVED_SUCC_MSG", "Member(s) approved successfully.");
define("MEMBER_DELETED_SUCC_MSG", "Member(s) deleted successfully.");

define("LEDGER_NAME_EXISTS", "Ledger name already exists");
define("ACL_UPLOAD_FOLDER_FRAGMENT", "cache/acl/menu_perms_");

define("BANNER_IMAGE_FILE_UPLOAD_PATH", APP_PATH . "var/banner/");
define("WELCOMEMSG_SUCC_MSG", "Society home details is added/edited successfully.");

define("EN", "en");
define("SITE_LOGO_HEIGHT", 120);
define("SOCLAYOUT_SUCC_MSG", "Society website layout details is added/edited successfully.");
define("GALLERY_IMG_FOLDER", "photogallery");
define("GALLERY_THUMB_IMG_FOLDER", "photogallery/thumb");
define("PRIORITY_SEND_IMMEDIATELY", 1);
define("ACCOUNT_MODULE_EXIST", 1);
define("PAYMENT_MODE_FOR_RECEIPT_REVERSAL", array("cheque", "cash", "cashtransfer"));
define("payment_mode_for_clearance", array("cheque", "dd"));
define("payment_mode_for_receipt_reversal", array("cheque", "dd"));
define("CHSONE_TINY_URL", 'http://cube1app.com'); //http://chs1.in
define("RCM_AMOUNT", 5000);
define("SMS_VIA", 'OneSociety');


//By Harshada K 15/03/2019
define("SOCCODE_LENGTH", 12);
define("VPA_LENGTH", 18);

//By Rajeshwar C 08/04/2019
define("OTHER_BANK_PG", 'OTHPG');
define("BENECODE_LENGTH", 50);
define("BENE_TRANSACTION_LIMIT", 100000);

define("DISPLAY_YES_BANK", 'YESPG');
define("DISPLAY_YES_BANK_ECOLLECT", 'srvybecollect');
define("DISPLAY_RBL_BANK", 'RBLPG');
define("DISPLAY_MOBIKWIK_WALLET", 'srvmobikwik');
define("DISPLAY_PAYTM", 'srvpaytm');
define("DISPLAY_MOBIKWIK_PG", 'srvmobikwikpg');
define("DISPLAY_CASHFREE_PG", 'CASHFREEPG');
define("DISPLAY_HDFC_PG", 'HDFCPG');
define("DISPLAY_ATOM_PG", 'ATOMPG');

define("DISPLAY_CASH_TRANSFER", 'Electronic Fund Transfer');
define("DISPLAY_YES_VPA", 'YESB0CMSNOC');

define("INVITIESLOGS", APP_PATH . "var/logs/invites");

if (array_key_exists('HTTP_HOST', $_SERVER)) {
    define("CURRENT_URL", (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]");
}
define("ACTIVE_PAYMENT_REVERSAL_DATE", "2018-09-29");
define("EXPENSE_ACTIVE_PAYMENT_REVERSAL_DATE", "2020-06-29");
define("APP_NAME_CHSONE", 'Society App');

// FSADMIN CONSTANTS
define("YES_BANK_PG", 'YESPG');
define("YES_BANK_ECOLLECT", 'srvybecollect');
define("PAYTM_PG", 'srvpaytm');
define("MOBIKWIK_WALLET", 'srvmobikwik');
define("MOBIKWIK_PG", 'srvmobikwikpg');
define("HDFC_PG", 'HDFCPG');
define("CASHFREE_PG", 'CASHFREEPG');
define("ATOM_PG", 'ATOMPG');