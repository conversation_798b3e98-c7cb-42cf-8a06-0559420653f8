<?php

namespace App\Console\Commands\Workflows\DCO\NOC;

use App\Console\Commands\Workflow;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class AddNOCWorkflow extends Workflow
{
    protected $signature = 'workflow:addNOC {input}';
    protected $description = 'Add NOC Workflow';

    public function apply()
    {
        // grab all incoming data
        $data = $this->request;

        // look up the dynamic “Bank Loan” purpose ID
        $templates  = $this->action('datasource:NOCTemplate', $this->pointer, $this->request);
        $bankLoanId = Arr::get(
            Arr::first($templates, fn($t) => Arr::get($t, 'purpose') === 'NOC Bank Loan'),
            'id'
        );

        // always require purpose
        $rules = [
            'purpose' => 'required|integer',
        ];

        // if it's not the Bank‐Loan purpose, require all the core fields
        if (($data['purpose'] ?? null) != $bankLoanId) {
            $rules += [
                'is_header_attached'      => 'required',
                'unit_id'                 => 'required|integer',
                'status'                  => 'required',
                'user_id'                 => 'required|integer',
                'society_name'            => 'required|string',
                'society_registration_no' => 'required|string',
                'address_line_1'          => 'required|string',
                'address_line_2'          => 'required|string',
                'letter_date'             => 'required|date',
                'aaplier_name'            => 'required|string',
                'relation_with_owner'     => 'required|string',
                'owner_name'              => 'required|string',
                'owner_address'           => 'required|string',
                'since_staying_date'      => 'required|date',
                'HIM_HER'                 => 'required|string',
                'HIS_HER'                 => 'required|string',
            ];
        }

        // always allow these fields if present
        $rules += [
            'authorized_signatory' => 'nullable|string',
            'bank_name'            => 'nullable|string',
            'bank_address'         => 'nullable|string',
            'flat_number'          => 'nullable',
            'cc_number'            => 'nullable|digits_between:13,19',
            'cc_expiry_date'       => 'nullable|date',
            'flat_cost'            => 'nullable|numeric',
        ];

        // custom error messages
        $messages = [
            'purpose.required' => 'Purpose is required',
            'purpose.integer'  => 'Purpose must be an integer',
        ];

        if (($data['purpose'] ?? null) != $bankLoanId) {
            foreach ([
                'is_header_attached'      => 'Is Header Attached is required',
                'unit_id'                 => 'Unit ID is required',
                'status'                  => 'Status is required',
                'user_id'                 => 'User ID is required',
                'society_name'            => 'Society Name is required',
                'society_registration_no' => 'Society Registration No is required',
                'address_line_1'          => 'Address Line 1 is required',
                'address_line_2'          => 'Address Line 2 is required',
                'letter_date'             => 'Letter Date is required',
                'aaplier_name'            => 'Applier Name is required',
                'relation_with_owner'     => 'Relation With Owner is required',
                'owner_name'              => 'Owner Name is required',
                'owner_address'           => 'Owner Address is required',
                'since_staying_date'      => 'Since Staying Date is required',
                'HIM_HER'                 => 'HIM/HER is required',
                'HIS_HER'                 => 'HIS/HER is required',
            ] as $field => $message) {
                $messages["{$field}.required"] = $message;
            }
        }

        // run the validator
        $validator = Validator::make($data, $rules, $messages);
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // everything’s valid—call the real datasource action
        $this->action('datasource:addNOC', $this->pointer, $this->request);
    }
}
